// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Sepesha';

  @override
  String get home => 'Home';

  @override
  String get trips => 'Trips';

  @override
  String get account => 'Account';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get swahili => 'Swahili';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get manageProfileDetails => 'Manage your profile details';

  @override
  String get paymentMethods => 'Payment Methods';

  @override
  String get managePaymentOptions => 'Manage your payment options';

  @override
  String get messages => 'Messages';

  @override
  String get viewConversations => 'View your conversations';

  @override
  String get appPreferencesSettings => 'App preferences and settings';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get getHelpSupport => 'Get help and support';

  @override
  String get about => 'About';

  @override
  String get appInformationVersion => 'App information and version';

  @override
  String get logout => 'Logout';

  @override
  String get signOutAccount => 'Sign out of your account';

  @override
  String get profile => 'Profile';

  @override
  String get edit => 'Edit';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get totalRides => 'Total Rides';

  @override
  String get rating => 'Rating';

  @override
  String get logoutConfirmation => 'Are you sure you want to logout from your account?';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get languageSettings => 'Language Settings';

  @override
  String get selectLanguage => 'Select your preferred language';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get notifications => 'Notifications';

  @override
  String get notificationsComingSoon => 'Notifications feature coming soon!';

  @override
  String get filterTrips => 'Filter trips feature coming soon!';

  @override
  String get user => 'USER';

  @override
  String get customer => 'CUSTOMER';

  @override
  String get driver => 'DRIVER';

  @override
  String get vendor => 'VENDOR';

  @override
  String get main => 'Main';

  @override
  String get support => 'Support';

  @override
  String get appInformation => 'App information';

  @override
  String get history => 'History';

  @override
  String get wallet => 'Wallet';

  @override
  String get manageWallet => 'Manage your wallet';

  @override
  String get yourDriverHasArrived => 'Your driver has arrived';

  @override
  String get isWaiting => 'is waiting';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get whatsappChat => 'WhatsApp Chat';

  @override
  String get quickResponseViaWhatsapp => 'Quick response via WhatsApp';

  @override
  String get phoneCall => 'Phone Call';

  @override
  String get speakDirectlyWithOurTeam => 'Speak directly with our team';

  @override
  String get version => 'Version';

  @override
  String get keyFeatures => 'Key Features';

  @override
  String get contactInformation => 'Contact Information';

  @override
  String get allRightsReserved => 'All rights reserved';

  @override
  String get saveTimeSaveMoneyAnd => 'Save time, save money and';

  @override
  String get safeRide => 'safe ride';

  @override
  String get useYourSmartphoneToOrder => 'Use your smartphone to order a ride, get picked up by a nearby driver, and enjoy a low-cost trip to your destination.';

  @override
  String get getConnectedWith => 'Get connected with';

  @override
  String get nearbyDrivers => 'nearby drivers';

  @override
  String get quicklyMatchWithReliable => 'Quickly match with reliable drivers around you for faster pickups and better service.';

  @override
  String get enjoyARideWith => 'Enjoy a ride with';

  @override
  String get fullComfort => 'full comfort';

  @override
  String get relaxInWellMaintained => 'Relax in well-maintained vehicles while your driver takes care of the road.';

  @override
  String get ridesCompleted => 'rides completed';
}
