// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Sepesha';

  @override
  String get deleteConversation => 'Delete Conversation';

  @override
  String get areYouSureDeleteConversation => 'Are you sure you want to delete this conversation with';

  @override
  String get home => 'Home';

  @override
  String helloWelcomeBack(Object userType) {
    return 'Hello, $userType! Welcome back';
  }

  @override
  String get enterPhoneVerification => 'Enter your phone number We\'ll send you a verification code';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get searchCountry => 'Search country...';

  @override
  String get driver => 'DRIVER';

  @override
  String get driverDescription => 'Login as a driver for easy gain request from customers and vendors';

  @override
  String get vendorBusiness => 'Vendor/Business';

  @override
  String get vendorDescription => 'Login as a vendor to manage your products and receive orders';

  @override
  String get customer => 'CUSTOMER';

  @override
  String get customerDescription => 'Login as a customer to request deliveries and order products';

  @override
  String get loginAsDriverOrVendor => 'Login as Driver or Vendor';

  @override
  String get loginAsCustomerOrVendor => 'Login as Customer or Vendor';

  @override
  String get loginAsCustomerOrDriver => 'Login as Customer or Driver';

  @override
  String get login => 'Login';

  @override
  String get notRegistered => 'Not registered? ';

  @override
  String get signUp => 'Sign Up';

  @override
  String get byContinuingYouAgree => 'By continuing, you agree to our ';

  @override
  String get termsConditions => 'Terms & Conditions';

  @override
  String get and => ' and ';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get youreOver18 => '. You\'re over 18.';

  @override
  String get getStarted => 'GET STARTED';

  @override
  String get next => 'Next';

  @override
  String get trips => 'Trips';

  @override
  String get skip => 'Skip';

  @override
  String get submitting => 'Submitting...';

  @override
  String get submitRating => 'Submit Rating';

  @override
  String get poor => 'Poor';

  @override
  String get fair => 'Fair';

  @override
  String get good => 'Good';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get veryGood => 'Very Good';

  @override
  String get excellent => 'Excellent';

  @override
  String get rateYourExperience => 'Rate your experience';

  @override
  String get errorSigningOut => 'Error signing out';

  @override
  String get account => 'Account';

  @override
  String get user => 'USER';

  @override
  String get totalRides => 'Total Rides';

  @override
  String get rating => 'Rating';

  @override
  String get quickStats => 'Quick Stats';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get manageProfileDetails => 'Manage your profile details';

  @override
  String get paymentMethods => 'Payment Methods';

  @override
  String get managePaymentOptions => 'Manage your payment options';

  @override
  String get messages => 'Messages';

  @override
  String get viewConversations => 'View your conversations';

  @override
  String get settings => 'Settings';

  @override
  String get appPreferencesSettings => 'App preferences and settings';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get getHelpSupport => 'Get help and support';

  @override
  String get about => 'About';

  @override
  String get appInformationVersion => 'App information and version';

  @override
  String get logout => 'Logout';

  @override
  String get signOutAccount => 'Sign out of your account';

  @override
  String get logoutConfirmation => 'Are you sure you want to logout from your account?';

  @override
  String get cancel => 'Cancel';

  @override
  String get searchConversations => 'Search conversations...';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get trySearchingWithDifferentKeywords => 'Try searching with different keywords';

  @override
  String get clearSearch => 'Clear Search';

  @override
  String get noConversationsYet => 'No conversations yet';

  @override
  String get startNewConversationToBeginMessaging => 'Start a new conversation to begin messaging';

  @override
  String get startNewChat => 'Start New Chat';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get markAllAsRead => 'Mark all as read';

  @override
  String get newConversation => 'New Conversation';

  @override
  String get searchContacts => 'Search contacts...';

  @override
  String get conversationDeleted => 'Conversation deleted';

  @override
  String get undo => 'Undo';

  @override
  String get allConversationsMarkedAsRead => 'All conversations marked as read';

  @override
  String get messageSettingsComingSoon => 'Message settings coming soon';

  @override
  String get confirmLocation => 'Confirm Location';

  @override
  String get setPickupLocation => 'Set Pickup Location';

  @override
  String get setDestination => 'Set Destination';

  @override
  String get startTypingToSearch => 'Start typing to search for locations';

  @override
  String get typeAtLeast2Characters => 'Type at least 2 characters';

  @override
  String get noLocationsFound => 'No locations found';

  @override
  String get driverFound => 'Driver Found';

  @override
  String get payment => 'Payment';

  @override
  String get destination => 'Destination:';

  @override
  String get isWaiting => 'is waiting';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get swahili => 'Swahili';

  @override
  String get profile => 'Profile';

  @override
  String get edit => 'Edit';

  @override
  String get save => 'Save';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get languageSettings => 'Language Settings';

  @override
  String get selectLanguage => 'Select your preferred language';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get notifications => 'Notifications';

  @override
  String get notificationsComingSoon => 'Notifications feature coming soon!';

  @override
  String get filterTrips => 'Filter trips feature coming soon!';

  @override
  String get vendor => 'VENDOR';

  @override
  String get main => 'Main';

  @override
  String get support => 'Support';

  @override
  String get appInformation => 'App information';

  @override
  String get history => 'History';

  @override
  String get wallet => 'Wallet';

  @override
  String get manageWallet => 'Manage your wallet';

  @override
  String get yourDriverHasArrived => 'Your driver has arrived';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get whatsappChat => 'WhatsApp Chat';

  @override
  String get quickResponseViaWhatsapp => 'Quick response via WhatsApp';

  @override
  String get phoneCall => 'Phone Call';

  @override
  String get speakDirectlyWithOurTeam => 'Speak directly with our team';

  @override
  String get version => 'Version';

  @override
  String get keyFeatures => 'Key Features';

  @override
  String get contactInformation => 'Contact Information';

  @override
  String get allRightsReserved => 'All rights reserved';

  @override
  String get saveTimeSaveMoneyAnd => 'Save time, save money and';

  @override
  String get safeRide => 'Safe ride';

  @override
  String get useYourSmartphoneToOrder => 'Use your smartphone to order a ride, get picked up by a nearby driver, and enjoy a low-cost trip to your destination.';

  @override
  String get getConnectedWith => 'Get connected with';

  @override
  String get nearbyDrivers => 'nearby drivers';

  @override
  String get quicklyMatchWithReliable => 'Quickly match with reliable drivers around you for faster pickups and better service.';

  @override
  String get enjoyARideWith => 'Enjoy a ride with';

  @override
  String get fullComfort => 'full comfort';

  @override
  String get relaxInWellMaintained => 'Relax in well-maintained vehicles while your driver takes care of the road.';

  @override
  String get ridesCompleted => 'Rides completed';

  @override
  String get vehicleInfo => 'Vehicle Information';

  @override
  String get vehicleType => 'Vehicle Type';

  @override
  String get vehicleNumber => 'Vehicle Number';

  @override
  String get licenseNumber => 'License Number';

  @override
  String get locationAccessRequired => 'Location Access Required';

  @override
  String get enableLocationPermissions => 'Tap to enable location permissions to go online';

  @override
  String get youAreOnline => 'You are Online';

  @override
  String get youAreOffline => 'You are Offline';

  @override
  String get waitingForRideRequests => 'Waiting for ride requests...';

  @override
  String get pickup => 'Pickup:';

  @override
  String get startRide => 'Start Ride';

  @override
  String get newRideRequest => 'New Ride Request';

  @override
  String get from => 'From:';

  @override
  String get to => 'To:';

  @override
  String get decline => 'Decline';

  @override
  String get accept => 'Accept';

  @override
  String get pickupLocation => 'Pickup Location';

  @override
  String get dropoffLocation => 'Dropoff Location';

  @override
  String get refreshLocation => 'Refresh Location';

  @override
  String get locationUpdated => 'Location updated:';

  @override
  String get couldNotGetLocation => 'Could not get current location. Check permissions.';

  @override
  String get areYouSureLogout => 'Are you sure you want to logout from your account?';

  @override
  String get locationServicesDisabled => 'Location Services Disabled';

  @override
  String get enableLocationServices => 'Location services are turned off. Please enable location services in your device settings to use this feature.';

  @override
  String get openSettings => 'Open Settings';

  @override
  String get locationPermissionRequired => 'Location Permission Required';

  @override
  String get enableLocationPermission => 'Location permission is required to go online as a driver. Please enable location permission in app settings.';

  @override
  String get locationPermissionDenied => 'Location Permission Denied';

  @override
  String get grantLocationPermission => 'Location permission is required to track your position as a driver. Please grant location permission to continue.';

  @override
  String get locationPermissionsEnabled => 'Location permissions are already enabled! You can go online.';

  @override
  String get unableToGetLocationPermissions => 'Unable to get location permissions. Please check your device settings.';

  @override
  String get locationPermissionsGranted => 'Location permissions granted! Getting your location...';

  @override
  String get locationFound => 'Location found! Map updated to your current position.';

  @override
  String get errorRequestingLocationPermission => 'Error requesting location permission:';

  @override
  String get driverEmail => '<EMAIL>';

  @override
  String get driverPhone => '+255000000000';

  @override
  String get notAvailable => 'N/A';

  @override
  String get driverProfile => 'Driver Profile';

  @override
  String get manageDriverProfile => 'Manage your driver profile';

  @override
  String get viewWalletBalance => 'View wallet balance and transactions';

  @override
  String get walletBalance => 'Wallet Balance';

  @override
  String get plateNumber => 'Plate Number';

  @override
  String get paymentPreference => 'Payment Preference';

  @override
  String get rideHistory => 'Ride History';

  @override
  String get active => 'Active';

  @override
  String get completed => 'Completed';

  @override
  String get canceled => 'Canceled';

  @override
  String get calculating => 'Calculating...';

  @override
  String get unknown => 'Unknown';

  @override
  String get driverNotAssigned => 'Driver not assigned';

  @override
  String get locationUnavailable => 'Location unavailable';

  @override
  String get calculatingArrival => 'Calculating arrival...';

  @override
  String get arrivingNow => 'Arriving now';

  @override
  String get arrivesInMinute => 'Arrives in 1 minute';

  @override
  String get arrivesInMinutes => 'Arrives in minutes';

  @override
  String get noActiveRides => 'No active rides';

  @override
  String get noCompletedRides => 'No completed rides';

  @override
  String get noCanceledRides => 'No canceled rides';

  @override
  String get unknownDriver => 'Unknown Driver';

  @override
  String get cost => 'COST';

  @override
  String get date => 'DATE';

  @override
  String get estimatedTripTime => 'Estimated trip time: ';

  @override
  String get contactDriver => 'Contact Driver';

  @override
  String get cancelRide => 'Cancel ride';

  @override
  String get rateThisRide => 'Rate This Ride';

  @override
  String get bookAgain => 'Book Again';

  @override
  String get tripDuration => 'Trip duration: ';

  @override
  String get chooseYourRide => 'Choose Your Ride';

  @override
  String get backToHome => 'Back to home';

  @override
  String get twoWheeler => '2 Wheeler';

  @override
  String get fourWheeler => '4 Wheeler';

  @override
  String get addLuggageSpace => 'Add Luggage Space';

  @override
  String get haveAPromoCode => 'Have a promo code?';

  @override
  String get enterPromoCode => 'Enter promo code';

  @override
  String get apply => 'Apply';

  @override
  String get continueButton => 'Continue';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get bodaboda => 'Bodaboda';

  @override
  String get bajaj => 'Bajaj';

  @override
  String get guta => 'Guta';

  @override
  String get carry => 'Carry';

  @override
  String get townace => 'Townace';

  @override
  String get capacity => 'Capacity: ';

  @override
  String get lookingForDriver => 'Looking for a driver';

  @override
  String get findingBestDriver => 'We\'re finding the best driver for you';

  @override
  String get tripInProgress => 'Trip in progress';

  @override
  String get distance => 'Distance';

  @override
  String get time => 'Time';

  @override
  String get price => 'Price';

  @override
  String get endTrip => 'End Trip';

  @override
  String get completeTrip => 'Complete Trip';

  @override
  String get areYouSureEndTrip => 'Are you sure you want to end this trip?';

  @override
  String get complete => 'Complete';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get profilePicture => 'Profile Picture';

  @override
  String get firstName => 'First Name';

  @override
  String get firstNameRequired => 'First name is required';

  @override
  String get middleName => 'Middle Name (Optional)';

  @override
  String get lastName => 'Last Name';

  @override
  String get lastNameRequired => 'Last name is required';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get invalidEmailFormat => 'Invalid email format';

  @override
  String get phoneNumberRequired => 'Phone number is required';

  @override
  String get phoneNumberMinDigits => 'Phone number must be at least 9 digits';

  @override
  String get region => 'Region';

  @override
  String get selectRegion => 'Select Region';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get errorSavingProfile => 'Error saving profile:';

  @override
  String get reviews => 'Reviews';

  @override
  String get failedToLoadReviews => 'Failed to load reviews';

  @override
  String get retry => 'Retry';

  @override
  String get noReviewsAvailable => 'No reviews available';

  @override
  String get basedOnCustomerFeedback => 'Based on customer feedback';

  @override
  String get anonymous => 'Anonymous';

  @override
  String get daysAgo => 'days ago';

  @override
  String get hoursAgo => 'hours ago';

  @override
  String get recently => 'Recently';

  @override
  String get findingDriver => 'Finding a driver...';

  @override
  String get reject => 'Reject';

  @override
  String get minutesToDelivery => 'Minutes to delivery';

  @override
  String get callRecipient => 'Call Recipient';

  @override
  String get startDropOffProcess => 'Start Drop off process';

  @override
  String get deliveryCompleted => 'Delivery Completed!';

  @override
  String get pleaseRateDriver => 'Please rate your driver...';

  @override
  String get cannotRateDriver => 'Cannot rate: Driver information not available';

  @override
  String get deliveryInProgress => 'Delivery in progress';

  @override
  String get driverName => 'Driver Name';

  @override
  String get driverStats => 'Driver Stats';

  @override
  String get selectLuggageSize => 'Select Luggage Size';

  @override
  String get personalItem => 'Personal Item';

  @override
  String get internationalCarryOn => 'International carry on';

  @override
  String get domesticCarryOn => 'Domestic Carry On';

  @override
  String get smallChecked => 'Small Checked';

  @override
  String get mediumChecked => 'Medium Checked';

  @override
  String get takeProofOfPickupParcel => 'Take proof of Pickup Parcel';

  @override
  String get requestARide => 'Request a Ride';

  @override
  String get rideAmount => 'Ride Amount:';

  @override
  String get additionalCost => 'Additional Cost:';

  @override
  String get total => 'Total:';

  @override
  String get kgs => 'kgs';

  @override
  String get yourTravelTakes => 'Your travel takes minutes';

  @override
  String get findingNearestRide => 'Finding the nearest Ride...';

  @override
  String get rideway => 'Rideway';

  @override
  String get affordableRides => 'Affordable rides, all to yourself';

  @override
  String get ridewaySuv => 'Rideway SUV';

  @override
  String get luxuryRides => 'Luxury rides';

  @override
  String get luggage => 'Luggage';

  @override
  String get discountCode => 'Discount code';

  @override
  String get enterDiscountCode => 'Enter Discount Code';

  @override
  String get enterCode => 'Enter code';

  @override
  String get aboutSepesha => 'About Sepesha';

  @override
  String get ourMission => 'Our Mission';

  @override
  String get supportEmail => 'Email: <EMAIL>';

  @override
  String get supportPhone => 'Phone: +255 123 456 789';

  @override
  String get website => 'Website: www.sepesha.com';

  @override
  String get address => 'Address:\nLorem ipsum street, 123\nDar es Salaam, Tanzania';

  @override
  String get legal => 'Legal';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get endUserLicenseAgreement => 'End User License Agreement';

  @override
  String get copyright => '© 2024 Sepesha. All rights reserved.';

  @override
  String get failedToLoadMessages => 'Failed to load messages';

  @override
  String get noMessagesYet => 'No messages yet';

  @override
  String get startConversation => 'Start the conversation by sending a message';

  @override
  String get typing => 'typing...';

  @override
  String get online => 'Online';

  @override
  String get lastSeenRecently => 'Last seen recently';

  @override
  String get viewProfile => 'View Profile';

  @override
  String get clearChat => 'Clear Chat';

  @override
  String get blockUser => 'Block User';

  @override
  String get areYouSureClearChat => 'Are you sure you want to clear this chat? This action cannot be undone.';

  @override
  String get areYouSureBlockUser => 'Are you sure you want to block this user?';

  @override
  String get block => 'Block';

  @override
  String get copy => 'Copy';

  @override
  String get reply => 'Reply';

  @override
  String get delete => 'Delete';

  @override
  String get messageCopied => 'Message copied to clipboard';

  @override
  String get messageDeleted => 'Message deleted';

  @override
  String get replyFunctionalityComingSoon => 'Reply functionality coming soon';

  @override
  String get voiceCallFeatureComingSoon => 'Voice call feature coming soon';

  @override
  String get videoCallFeatureComingSoon => 'Video call feature coming soon';

  @override
  String get profileViewComingSoon => 'Profile view coming soon';

  @override
  String get chatCleared => 'Chat cleared';

  @override
  String get userBlocked => 'User blocked';

  @override
  String get saveImageComingSoon => 'Save image coming soon';

  @override
  String get failedToLoadImage => 'Failed to load image';

  @override
  String get failedToShareLocation => 'Failed to share location: ';

  @override
  String get userType => 'User Type';

  @override
  String get accountStatus => 'Account Status';

  @override
  String get verified => 'Verified';

  @override
  String get unverified => 'Unverified';

  @override
  String get walletBalanceTzs => 'Wallet Balance (TZS)';

  @override
  String get walletBalanceUsd => 'Wallet Balance (USD)';

  @override
  String get accountInformation => 'Account Information';

  @override
  String get success => 'Success';

  @override
  String get profileUpdatedSuccessfully => 'Profile updated successfully';

  @override
  String get error => 'Error';

  @override
  String get failedToUpdateProfile => 'Failed to update profile';

  @override
  String get noProfileDataAvailable => 'No profile data available';

  @override
  String get enterLocation => 'Enter location';

  @override
  String get currentLocation => 'Current Location';

  @override
  String get loadingPaymentMethods => 'Loading payment methods...';

  @override
  String get paymentMethodUpdated => 'Payment method updated';

  @override
  String get failedToUpdatePaymentMethod => 'Failed to update payment method';

  @override
  String get walletBalanceDetails => 'Wallet Balance Details';

  @override
  String get tzsBalance => 'TZS Balance';

  @override
  String get usdBalance => 'USD Balance';

  @override
  String get walletReadyForPayments => 'Your wallet is ready for payments';

  @override
  String get addFundsToWallet => 'Add funds to your wallet to use this payment method';

  @override
  String get unableToLoadWalletBalance => 'Unable to load wallet balance';

  @override
  String get close => 'Close';

  @override
  String get refresh => 'Refresh';

  @override
  String get done => 'Done';

  @override
  String get selected => 'Selected';

  @override
  String get searchRide => 'Search Ride';

  @override
  String get whereAreYouGoing => 'Where are you going?';

  @override
  String get findRide => 'Find Ride';

  @override
  String get accountStatistics => 'Account Statistics';

  @override
  String get averageRating => 'Average Rating';

  @override
  String get walletTzs => 'Wallet (TZS)';

  @override
  String get walletUsd => 'Wallet (USD)';

  @override
  String get preferredPaymentMethod => 'Preferred Payment Method';

  @override
  String get verifiedAccount => 'Verified Account';

  @override
  String get pendingVerification => 'Pending Verification';
}
