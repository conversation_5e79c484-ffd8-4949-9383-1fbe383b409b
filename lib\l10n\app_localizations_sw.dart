// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swahili (`sw`).
class AppLocalizationsSw extends AppLocalizations {
  AppLocalizationsSw([String locale = 'sw']) : super(locale);

  @override
  String get appName => 'Sepesha';

  @override
  String get home => 'Nyumbani';

  @override
  String get trips => 'Safari';

  @override
  String get account => 'Akaunti';

  @override
  String get settings => 'Mipangilio';

  @override
  String get language => 'Lugha';

  @override
  String get english => 'Kiingereza';

  @override
  String get swahili => 'Kiswahili';

  @override
  String get personalInformation => 'Maelezo ya Kibinafsi';

  @override
  String get manageProfileDetails => 'Dhibiti maelezo ya wasifu wako';

  @override
  String get paymentMethods => 'Njia za Malipo';

  @override
  String get managePaymentOptions => 'Dhibiti chaguo za malipo yako';

  @override
  String get messages => 'Ujumbe';

  @override
  String get viewConversations => 'Ona mazungumzo yako';

  @override
  String get appPreferencesSettings => 'Mapendeleo ya programu na mipangilio';

  @override
  String get helpSupport => 'Msaada na Uongozi';

  @override
  String get getHelpSupport => 'Pata msaada na uongozi';

  @override
  String get about => 'Kuhusu';

  @override
  String get appInformationVersion => 'Maelezo ya programu na toleo';

  @override
  String get logout => 'Toka';

  @override
  String get signOutAccount => 'Toka kwenye akaunti yako';

  @override
  String get profile => 'Wasifu';

  @override
  String get edit => 'Hariri';

  @override
  String get cancel => 'Ghairi';

  @override
  String get save => 'Hifadhi';

  @override
  String get totalRides => 'Jumla ya Safari';

  @override
  String get rating => 'Kiwango';

  @override
  String get logoutConfirmation => 'Je, una uhakika unataka kutoka kwenye akaunti yako?';

  @override
  String get yes => 'Ndiyo';

  @override
  String get no => 'Hapana';

  @override
  String get languageSettings => 'Mipangilio ya Lugha';

  @override
  String get selectLanguage => 'Chagua lugha unayopendelea';

  @override
  String get languageChanged => 'Lugha imebadilishwa kwa mafanikio';

  @override
  String get notifications => 'Arifa';

  @override
  String get notificationsComingSoon => 'Kipengele cha arifa kinakuja hivi karibuni!';

  @override
  String get filterTrips => 'Kipengele cha kuchuja safari kinakuja hivi karibuni!';

  @override
  String get user => 'MTUMIAJI';

  @override
  String get customer => 'MTEJA';

  @override
  String get driver => 'DEREVA';

  @override
  String get vendor => 'MUUZAJI';

  @override
  String get main => 'Kuu';

  @override
  String get support => 'Msaada';

  @override
  String get appInformation => 'Maelezo ya programu';

  @override
  String get history => 'Historia';

  @override
  String get wallet => 'Mkoba';

  @override
  String get manageWallet => 'Dhibiti mkoba wako';

  @override
  String get yourDriverHasArrived => 'Dereva wako amefika';

  @override
  String get isWaiting => 'anasubiri';

  @override
  String get contactUs => 'Wasiliana Nasi';

  @override
  String get whatsappChat => 'Mazungumzo ya WhatsApp';

  @override
  String get quickResponseViaWhatsapp => 'Jibu la haraka kupitia WhatsApp';

  @override
  String get phoneCall => 'Simu';

  @override
  String get speakDirectlyWithOurTeam => 'Zungumza moja kwa moja na timu yetu';

  @override
  String get version => 'Toleo';

  @override
  String get keyFeatures => 'Vipengele Muhimu';

  @override
  String get contactInformation => 'Maelezo ya Mawasiliano';

  @override
  String get allRightsReserved => 'Haki zote zimehifadhiwa';

  @override
  String get saveTimeSaveMoneyAnd => 'Okoa muda, okoa pesa na';

  @override
  String get safeRide => 'safari salama';

  @override
  String get useYourSmartphoneToOrder => 'Tumia simu yako kuagiza safari, uchukuwe na dereva wa karibu, na ufurahie safari ya bei nafuu kwenda mahali ulipokusudia.';

  @override
  String get getConnectedWith => 'Unganishwa na';

  @override
  String get nearbyDrivers => 'madereva wa karibu';

  @override
  String get quicklyMatchWithReliable => 'Unganishwa haraka na madereva wa kuaminika karibu nawe kwa ajili ya kuchukua haraka na huduma bora.';

  @override
  String get enjoyARideWith => 'Furahia safari na';

  @override
  String get fullComfort => 'starehe kamili';

  @override
  String get relaxInWellMaintained => 'Pumzika katika magari yaliyotunzwa vizuri wakati dereva wako anashughulikia barabara.';

  @override
  String get ridesCompleted => ' : idadi ya safari zilizokamilika';
}
