// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Swahili (`sw`).
class AppLocalizationsSw extends AppLocalizations {
  AppLocalizationsSw([String locale = 'sw']) : super(locale);

  @override
  String get appName => 'Sepesha';

  @override
  String get home => 'Nyumbani';

  @override
  String get trips => 'Safari';

  @override
  String get account => 'Akaunti';

  @override
  String get settings => 'Mipangilio';

  @override
  String get language => 'Lugha';

  @override
  String get english => 'Kiingereza';

  @override
  String get swahili => 'Kiswahili';

  @override
  String get personalInformation => 'Maelezo ya Kibinafsi';

  @override
  String get manageProfileDetails => 'Dhibiti maelezo ya wasifu wako';

  @override
  String get paymentMethods => 'Njia za Malipo';

  @override
  String get managePaymentOptions => 'Dhibiti chaguo za malipo yako';

  @override
  String get messages => 'Ujumbe';

  @override
  String get viewConversations => 'Ona mazungumzo yako';

  @override
  String get appPreferencesSettings => 'Mapendeleo ya programu na mipangilio';

  @override
  String get helpSupport => 'Msaada na Uongozi';

  @override
  String get getHelpSupport => 'Pata msaada na uongozi';

  @override
  String get about => 'Kuhusu';

  @override
  String get appInformationVersion => 'Maelezo ya programu na toleo';

  @override
  String get logout => 'Toka';

  @override
  String get signOutAccount => 'Toka kwenye akaunti yako';

  @override
  String get profile => 'Wasifu';

  @override
  String get edit => 'Hariri';

  @override
  String get cancel => 'Ghairi';

  @override
  String get save => 'Hifadhi';

  @override
  String get totalRides => 'Jumla ya Safari';

  @override
  String get rating => 'Kiwango';

  @override
  String get logoutConfirmation =>
      'Je, una uhakika unataka kutoka kwenye akaunti yako?';

  @override
  String get yes => 'Ndiyo';

  @override
  String get no => 'Hapana';

  @override
  String get languageSettings => 'Mipangilio ya Lugha';

  @override
  String get selectLanguage => 'Chagua lugha unayopendelea';

  @override
  String get languageChanged => 'Lugha imebadilishwa kwa mafanikio';

  @override
  String get notifications => 'Arifa';

  @override
  String get notificationsComingSoon =>
      'Kipengele cha arifa kinakuja hivi karibuni!';

  @override
  String get filterTrips =>
      'Kipengele cha kuchuja safari kinakuja hivi karibuni!';

  @override
  String get user => 'MTUMIAJI';

  @override
  String get customer => 'MTEJA';

  @override
  String get driver => 'DEREVA';

  @override
  String get vendor => 'MUUZAJI';
}
